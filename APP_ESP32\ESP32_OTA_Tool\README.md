# NimBLE OTA Client - Standalone Executable

This is a standalone Windows executable for performing Over-The-Air (OTA) firmware updates on ESP32 devices via Bluetooth Low Energy (BLE).

## Features

- **Standalone executable** - No Python installation required
- **BLE OTA updates** - Wirelessly update ESP32 firmware
- **Device scanning** - Automatically find ESP32 OTA devices
- **Interactive selection** - Choose from multiple found devices
- **Progress tracking** - Real-time transfer progress and statistics
- **Error handling** - Robust error detection and recovery
- **Resume capability** - Resume interrupted transfers
- **Security support** - Optional BLE security and bonding

## Quick Start

### 1. Scan for ESP32 devices
```cmd
nimble_ota_client.exe --scan
```

### 2. Update firmware
```cmd
nimble_ota_client.exe firmware.bin --verbose
```

### 3. Update specific device
```cmd
nimble_ota_client.exe firmware.bin --address A0:85:E3:F1:8C:C6
```

## Command Line Options

```
usage: nimble_ota_client.exe [-h] [-a ADDRESS] [-s {0,1}] [-v] [--scan] [--status] [--abort] [--security] [firmware]

positional arguments:
  firmware              Path to firmware binary file

options:
  -h, --help            Show help message and exit
  -a ADDRESS, --address ADDRESS
                        ESP32 BLE device address (e.g., A0:85:E3:F1:8C:C6)
  -s {0,1}, --slot {0,1}
                        Target OTA slot (0 or 1, default: 0)
  -v, --verbose         Enable verbose logging
  --scan                Scan for devices and exit
  --status              Read device status and exit
  --abort               Abort current transfer and exit
  --security            Enable BLE security and bonding
  --auto-select         Automatically select first available device (no interactive selection)
```

## Usage Examples

### Basic firmware update
```cmd
nimble_ota_client.exe my_firmware.bin
```

### Verbose firmware update with progress
```cmd
nimble_ota_client.exe my_firmware.bin --verbose
```

### Update specific device by MAC address
```cmd
nimble_ota_client.exe my_firmware.bin --address A0:85:E3:F1:8C:C6 --verbose
```

### Scan for available devices
```cmd
nimble_ota_client.exe --scan
```

### Check device status
```cmd
nimble_ota_client.exe --status --address A0:85:E3:F1:8C:C6
```

### Abort ongoing transfer
```cmd
nimble_ota_client.exe --abort --address A0:85:E3:F1:8C:C6
```

### Auto-select first device (skip interactive selection)
```cmd
nimble_ota_client.exe my_firmware.bin --auto-select
```

## Interactive Device Selection

When multiple ESP32 devices are found, the tool will display a list for you to choose from:

```
Found 2 target devices:
============================================================
  1. MR Car 44 (A0:85:E3:F1:8C:C6)
  2. MR CAR 1 (B4:3A:45:F7:39:0A)
============================================================
Select device (1-2) or 'q' to quit: 1
Selected: MR Car 44 (A0:85:E3:F1:8C:C6)
```

- **Type the number** (1, 2, etc.) to select a device
- **Type 'q'** to quit without updating
- **Press Ctrl+C** to cancel at any time

## Supported Devices

The client automatically detects ESP32 devices with the following naming patterns:
- Devices starting with "MR" (e.g., "MR Car 44")
- Devices starting with "CT" 
- Devices starting with "COP"
- Devices containing "ESP32"
- Specific MAC address: A0:85:E3:F1:8C:C6

## Firmware Requirements

- **File format**: Binary (.bin) files
- **Size limit**: Up to 2MB (typical ESP32 OTA partition size)
- **Compatibility**: ESP32 with NimBLE OTA service

## Transfer Process

1. **Device Discovery**: Scans for compatible ESP32 devices
2. **Connection**: Establishes BLE connection
3. **Authentication**: Optional security handshake
4. **Transfer**: Sends firmware in 500-byte chunks
5. **Verification**: ESP32 verifies firmware integrity
6. **Restart**: ESP32 automatically restarts with new firmware

## Error Handling

### Common Issues

**"No ESP32 devices found"**
- Ensure ESP32 is powered on and advertising
- Check if device is in OTA mode
- Try scanning multiple times

**"Connection failed"**
- Device may be out of range
- Another client may be connected
- Try restarting the ESP32

**"Same version detected"**
- ESP32 rejects same/older firmware versions
- Update version in CMakeLists.txt and rebuild firmware

**"Transfer failed"**
- Check BLE connection stability
- Ensure sufficient power to ESP32
- Try reducing transfer speed with verbose mode

## Performance

- **Transfer speed**: ~50-100 chunks/second (25-50 KB/s)
- **Chunk size**: 500 bytes per chunk
- **Typical 1MB firmware**: 30-60 seconds
- **Connection timeout**: 30 seconds
- **Chunk timeout**: 15 seconds

## Technical Details

- **BLE Service UUID**: def09abc-5678-1234-def0-9abc56781234
- **MTU**: Automatically negotiated (up to 512 bytes)
- **Security**: Optional BLE bonding with "Just Works" pairing
- **Resume**: Supports resuming interrupted transfers
- **CRC**: Firmware integrity verification

## Troubleshooting

### Enable verbose logging
```cmd
nimble_ota_client.exe firmware.bin --verbose
```

### Check Windows BLE support
- Ensure Bluetooth is enabled in Windows
- Update Bluetooth drivers if needed
- Some USB Bluetooth adapters may not support BLE

### ESP32 side debugging
- Check ESP32 serial output for error messages
- Verify OTA partition configuration
- Ensure sufficient flash space

## File Information

- **Executable size**: ~11.2 MB
- **Dependencies**: All included (no installation required)
- **Platform**: Windows 10/11 (64-bit)
- **Python version**: Built with Python 3.11.9
- **BLE library**: Bleak 0.22.3 with WinRT backend

## Version History

- **v1.0**: Initial standalone executable release
- Based on nimble_ota_client.py with full BLE OTA functionality

## Support

For issues or questions:
1. Check ESP32 serial output for error messages
2. Run with `--verbose` flag for detailed logging
3. Verify firmware file is valid and newer version
4. Ensure ESP32 is in OTA mode and advertising
