{"sources": [{"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/CMakeFiles/bootloader"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/CMakeFiles/bootloader.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/BLE/Bitbucket/esp32/APP_ESP32/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}