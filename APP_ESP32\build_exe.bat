@echo off
REM Build script for NimBLE OTA Client executable
REM This script creates a standalone .exe file for easier deployment

echo ========================================
echo Building NimBLE OTA Client Executable
echo ========================================

REM Check if virtual environment exists
if not exist "python_testing\bin\python" (
    echo Error: Virtual environment not found at python_testing\bin\
    echo Please ensure the virtual environment is set up correctly.
    pause
    exit /b 1
)

REM Check if PyInstaller is installed
python_testing\bin\python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo PyInstaller not found. Installing...
    python_testing\bin\python -m pip install pyinstaller
    if errorlevel 1 (
        echo Failed to install PyInstaller
        pause
        exit /b 1
    )
)

REM Clean previous builds
echo Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

REM Build the executable using the spec file
echo Building executable...
python_testing\bin\python -m PyInstaller nimble_ota_client.spec

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

REM Check if executable was created
if exist "dist\nimble_ota_client.exe" (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo Executable location: dist\nimble_ota_client.exe
    echo File size: 
    for %%A in ("dist\nimble_ota_client.exe") do echo   %%~zA bytes
    echo.
    echo Usage examples:
    echo   dist\nimble_ota_client.exe --scan
    echo   dist\nimble_ota_client.exe firmware.bin --verbose
    echo   dist\nimble_ota_client.exe --help
    echo.
) else (
    echo Build failed - executable not found!
    exit /b 1
)

pause
