
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named org - imported by pickle (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named 'bleak_winrt.windows.system' - imported by bleak_winrt.windows.storage.streams (optional)
missing module named 'bleak_winrt.windows.ui' - imported by bleak_winrt.windows.devices.enumeration (optional)
missing module named 'bleak_winrt.windows.security' - imported by bleak_winrt.windows.devices.enumeration (optional)
missing module named 'bleak_winrt.windows.applicationmodel' - imported by bleak_winrt.windows.devices.enumeration (optional)
missing module named 'bleak_winrt.windows.networking' - imported by bleak_winrt.windows.devices.bluetooth (optional)
missing module named 'bleak_winrt.windows.devices.radios' - imported by bleak_winrt.windows.devices.bluetooth (optional)
missing module named 'bleak_winrt.windows.devices.bluetooth.rfcomm' - imported by bleak_winrt.windows.devices.bluetooth (optional)
missing module named 'winrt.windows.system' - imported by winrt._winrt_windows_storage_streams (top-level)
missing module named 'winrt.windows.ui' - imported by winrt._winrt_windows_devices_enumeration (top-level)
missing module named 'winrt.windows.security' - imported by winrt._winrt_windows_devices_enumeration (top-level)
missing module named 'winrt.windows.applicationmodel' - imported by winrt._winrt_windows_devices_enumeration (top-level)
missing module named 'winrt.windows.networking' - imported by winrt._winrt_windows_devices_bluetooth (top-level)
missing module named 'winrt.windows.devices.radios' - imported by winrt._winrt_windows_devices_bluetooth (top-level)
missing module named 'winrt.windows.devices.bluetooth.rfcomm' - imported by winrt._winrt_windows_devices_bluetooth (top-level)
missing module named libdispatch - imported by bleak.backends.corebluetooth.CentralManagerDelegate (top-level)
missing module named Foundation - imported by bleak.backends.corebluetooth.scanner (top-level), bleak.backends.corebluetooth.CentralManagerDelegate (top-level), bleak.backends.corebluetooth.utils (top-level), bleak.backends.corebluetooth.client (top-level), bleak.backends.corebluetooth.PeripheralDelegate (top-level)
missing module named CoreBluetooth - imported by bleak.backends.corebluetooth.scanner (top-level), bleak.backends.corebluetooth.CentralManagerDelegate (top-level), bleak.backends.corebluetooth.utils (top-level), bleak.backends.corebluetooth.client (top-level), bleak.backends.corebluetooth.characteristic (top-level), bleak.backends.corebluetooth.descriptor (top-level), bleak.backends.corebluetooth.PeripheralDelegate (top-level), bleak.backends.corebluetooth.service (top-level)
missing module named objc - imported by bleak.backends.corebluetooth (top-level), bleak.backends.corebluetooth.scanner (top-level), bleak.backends.corebluetooth.CentralManagerDelegate (top-level), bleak.backends.corebluetooth.PeripheralDelegate (top-level)
missing module named 'dbus_fast.message' - imported by bleak.backends.bluezdbus.client (top-level), bleak.backends.bluezdbus.utils (top-level), bleak.backends.bluezdbus.signals (top-level)
missing module named 'dbus_fast.constants' - imported by bleak.backends.bluezdbus.client (top-level), bleak.backends.bluezdbus.utils (top-level)
missing module named 'dbus_fast.auth' - imported by bleak.backends.bluezdbus.utils (top-level)
missing module named 'dbus_fast.validators' - imported by bleak.backends.bluezdbus.signals (top-level)
missing module named 'dbus_fast.errors' - imported by bleak.backends.bluezdbus.signals (top-level)
missing module named 'dbus_fast.aio' - imported by bleak.backends.bluezdbus.manager (top-level), bleak.backends.bluezdbus.signals (top-level)
missing module named 'dbus_fast.service' - imported by bleak.backends.bluezdbus.advertisement_monitor (top-level)
missing module named dbus_fast - imported by bleak.backends.bluezdbus.client (top-level), bleak.backends.bluezdbus.manager (top-level), bleak.backends.bluezdbus.scanner (top-level)
missing module named jnius - imported by bleak.backends.p4android.client (top-level), bleak.backends.p4android.defs (top-level), bleak.backends.p4android.utils (top-level), bleak.backends.p4android.scanner (top-level)
missing module named 'android.permissions' - imported by bleak.backends.p4android.scanner (top-level)
missing module named 'android.broadcast' - imported by bleak.backends.p4android.scanner (top-level)
missing module named 'dbus_fast.signature' - imported by bleak.backends.bluezdbus.client (top-level)
missing module named android - imported by bleak.backends.p4android.client (top-level)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
