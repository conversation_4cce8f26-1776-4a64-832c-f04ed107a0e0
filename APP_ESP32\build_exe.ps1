# PowerShell build script for NimBLE OTA Client executable
# This script creates a standalone .exe file for easier deployment

Write-Host "========================================" -ForegroundColor Green
Write-Host "Building NimBLE OTA Client Executable" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if virtual environment exists
if (-not (Test-Path "python_testing\bin\python")) {
    Write-Host "Error: Virtual environment not found at python_testing\bin\" -ForegroundColor Red
    Write-Host "Please ensure the virtual environment is set up correctly." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if PyInstaller is installed
try {
    & "python_testing\bin\python" -m PyInstaller --version | Out-Null
    Write-Host "PyInstaller found" -ForegroundColor Green
} catch {
    Write-Host "PyInstaller not found. Installing..." -ForegroundColor Yellow
    try {
        & "python_testing\bin\python" -m pip install pyinstaller
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to install PyInstaller"
        }
        Write-Host "PyInstaller installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to install PyInstaller: $_" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Clean previous builds
Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "__pycache__") { Remove-Item -Recurse -Force "__pycache__" }

# Build the executable using the spec file
Write-Host "Building executable..." -ForegroundColor Yellow
try {
    & "python_testing\bin\python" -m PyInstaller nimble_ota_client.spec
    if ($LASTEXITCODE -ne 0) {
        throw "PyInstaller build failed"
    }
} catch {
    Write-Host "Build failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if executable was created
if (Test-Path "dist\nimble_ota_client.exe") {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Executable location: dist\nimble_ota_client.exe" -ForegroundColor Cyan
    
    $fileSize = (Get-Item "dist\nimble_ota_client.exe").Length
    Write-Host "File size: $($fileSize) bytes ($([math]::Round($fileSize/1MB, 2)) MB)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage examples:" -ForegroundColor Yellow
    Write-Host "  dist\nimble_ota_client.exe --scan" -ForegroundColor White
    Write-Host "  dist\nimble_ota_client.exe firmware.bin --verbose" -ForegroundColor White
    Write-Host "  dist\nimble_ota_client.exe --help" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "Build failed - executable not found!" -ForegroundColor Red
    exit 1
}

Read-Host "Press Enter to exit"
