# Interactive Device Selection Demo

This document shows how the new interactive device selection feature works.

## Scenario: Multiple ESP32 Devices Found

When you run the OTA client and multiple ESP32 devices are detected, you'll see an interactive menu:

### Example Output:

```
D:\ESP32_OTA_Tool> nimble_ota_client.exe firmware.bin

[INFO] Scanning for ESP32 OTA devices...
[INFO] Found 56 BLE devices during scan:
[INFO] Found target device: MR Car 44 (A0:85:E3:F1:8C:C6)
[INFO] Found target device: MR CAR 1 (B4:3A:45:F7:39:0A)

Found 2 target devices:
============================================================
  1. MR Car 44 (A0:85:E3:F1:8C:C6)
  2. MR CAR 1 (B4:3A:45:F7:39:0A)
============================================================
Select device (1-2) or 'q' to quit: 1
Selected: MR Car 44 (A0:85:E3:F1:8C:C6)

Connected to A0:85:E3:F1:8C:C6
OTA service found!
Firmware: firmware.bin
Size: 1234567 bytes (1.2 MB)
Starting OTA transfer...
```

## User Options:

### 1. Select a Device
- Type `1` to select the first device
- Type `2` to select the second device
- And so on...

### 2. Cancel the Operation
- Type `q` to quit without updating
- Press `Ctrl+C` to cancel at any time

### 3. Auto-Select Mode
If you don't want interactive selection, use the `--auto-select` flag:

```cmd
nimble_ota_client.exe firmware.bin --auto-select
```

This will automatically select the first available device without prompting.

## Benefits:

✅ **Clear device identification** - See device names and MAC addresses  
✅ **User control** - Choose exactly which device to update  
✅ **Safety** - Prevents accidental updates to wrong devices  
✅ **Flexibility** - Can still use auto-select for scripting  

## Use Cases:

### Development Environment
- Multiple ESP32 prototypes on your desk
- Each device has a different name/purpose
- Select the specific one you want to update

### Production Environment
- Use `--auto-select` for automated scripts
- Or specify exact MAC address with `-a` parameter

### Testing Environment
- Multiple test devices
- Interactive selection helps avoid confusion
- Clear identification of each device
