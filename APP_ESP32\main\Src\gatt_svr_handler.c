/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : gatt_svr_handler.c
* @version        : 1.0.8
* @brief          : Handle the GATT service and its callbacks
* @details        : Handle the GATT service and its callbacks
*****************************************************************************
* @version 1.0.1                                Date : 09/07/2025
* [~] Modified the function name of config file handler
* [+] Added support to Write Config File
*****************************************************************************
* @version 1.0.2                                Date : 18/07/2025
* [~] Modified the function name of config file handler
* [+] Added support to FW File transfer
*****************************************************************************
* @version 1.0.3                                Date : 23/07/2025
* [+] Added LED control functionality to indicate BLE activity
*****************************************************************************
* @version 1.0.4                                Date : 24/07/2025
* [+] Added Notification send to mobile if error in packet
*****************************************************************************
* @version 1.0.5                                Date : 26/07/2025
* [+] Added Version Request Support
*****************************************************************************
* @version 1.0.6                                Date : 26/07/2025
* [+] Added Authentication Callback
*****************************************************************************
* @version 1.0.7                                Date : 29/07/2025
* [+] Added SECURITY Macro to enable and disable authentication
*****************************************************************************
* @version 1.0.8                                Date : 31/07/2025
* [+] moved from if to switch in notification handling
*******************************************************************************/

#include "stdint.h"
#include <inttypes.h>
#include "gatt_svr_handler.h"
#include "uart_handler.h"
#include "ble_request_handler.h"
#include "utility.h"
#include "led_control.h"
#include "node_version.h"
#include "mbedtls/pk.h"
#include "mbedtls/rsa.h"
#include "mbedtls/entropy.h"
#include "mbedtls/ctr_drbg.h"
#include "esp_timer.h"

/* DEFINES */

void notify_client(uint16_t conn_handle, uint16_t attr_handle, uint8_t err_code);
void notify_client_data(uint16_t conn_handle, uint16_t attr_handle, uint8_t* data, uint16_t len);

extern uint16_t config_att_handle;
extern uint16_t fw_file_att_handle;
extern uint16_t req_resp_att_handle;
extern uint16_t auth_att_handle;
extern bool version_request_flag;
extern uint16_t get_connection_handle(void);

static uint8_t challenge_buf[32];
static int challenge_len = 0;
bool is_authenticated = false;
volatile bool is_fw_upgrade_in_progress = false;
bool ota_mode_active = false;  // Flag to bypass security during OTA
unsigned char rsa_public_pem[] = {
  0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x20, 0x50,
  0x55, 0x42, 0x4c, 0x49, 0x43, 0x20, 0x4b, 0x45, 0x59, 0x2d, 0x2d, 0x2d,
  0x2d, 0x2d, 0x0a, 0x4d, 0x49, 0x49, 0x42, 0x49, 0x6a, 0x41, 0x4e, 0x42,
  0x67, 0x6b, 0x71, 0x68, 0x6b, 0x69, 0x47, 0x39, 0x77, 0x30, 0x42, 0x41,
  0x51, 0x45, 0x46, 0x41, 0x41, 0x4f, 0x43, 0x41, 0x51, 0x38, 0x41, 0x4d,
  0x49, 0x49, 0x42, 0x43, 0x67, 0x4b, 0x43, 0x41, 0x51, 0x45, 0x41, 0x30,
  0x58, 0x5a, 0x77, 0x59, 0x2f, 0x71, 0x58, 0x54, 0x4b, 0x79, 0x59, 0x44,
  0x69, 0x65, 0x36, 0x78, 0x2f, 0x74, 0x4a, 0x0a, 0x2b, 0x35, 0x39, 0x47,
  0x75, 0x4d, 0x50, 0x6d, 0x5a, 0x6b, 0x66, 0x50, 0x52, 0x6c, 0x4d, 0x33,
  0x4e, 0x73, 0x56, 0x6d, 0x4b, 0x4c, 0x55, 0x35, 0x6e, 0x4e, 0x52, 0x6e,
  0x78, 0x42, 0x54, 0x78, 0x6b, 0x52, 0x6e, 0x33, 0x58, 0x45, 0x39, 0x35,
  0x34, 0x4c, 0x73, 0x7a, 0x68, 0x6b, 0x6a, 0x4a, 0x5a, 0x4c, 0x56, 0x79,
  0x33, 0x50, 0x71, 0x6b, 0x37, 0x54, 0x46, 0x49, 0x46, 0x2b, 0x2f, 0x64,
  0x0a, 0x70, 0x52, 0x57, 0x61, 0x67, 0x4d, 0x2f, 0x62, 0x4b, 0x33, 0x63,
  0x73, 0x57, 0x56, 0x5a, 0x65, 0x38, 0x4d, 0x4b, 0x36, 0x66, 0x6a, 0x48,
  0x38, 0x51, 0x4b, 0x2f, 0x72, 0x36, 0x37, 0x62, 0x2b, 0x53, 0x6e, 0x6f,
  0x53, 0x36, 0x38, 0x6f, 0x38, 0x4d, 0x74, 0x66, 0x79, 0x6a, 0x43, 0x52,
  0x6b, 0x6c, 0x77, 0x63, 0x38, 0x32, 0x30, 0x48, 0x55, 0x68, 0x44, 0x6d,
  0x42, 0x44, 0x67, 0x77, 0x6e, 0x0a, 0x63, 0x38, 0x33, 0x41, 0x6d, 0x35,
  0x6d, 0x6b, 0x76, 0x61, 0x6f, 0x33, 0x6f, 0x38, 0x74, 0x5a, 0x59, 0x4b,
  0x4f, 0x44, 0x43, 0x71, 0x46, 0x48, 0x52, 0x47, 0x73, 0x48, 0x5a, 0x55,
  0x50, 0x66, 0x43, 0x44, 0x6f, 0x55, 0x52, 0x67, 0x32, 0x51, 0x6e, 0x52,
  0x79, 0x65, 0x71, 0x58, 0x75, 0x5a, 0x45, 0x79, 0x71, 0x4c, 0x48, 0x31,
  0x43, 0x51, 0x35, 0x53, 0x55, 0x54, 0x62, 0x37, 0x68, 0x2b, 0x0a, 0x77,
  0x58, 0x58, 0x64, 0x79, 0x6a, 0x44, 0x59, 0x66, 0x38, 0x48, 0x67, 0x44,
  0x5a, 0x63, 0x72, 0x45, 0x6d, 0x79, 0x70, 0x6d, 0x2f, 0x62, 0x51, 0x6e,
  0x72, 0x4f, 0x65, 0x66, 0x53, 0x53, 0x2b, 0x67, 0x64, 0x38, 0x4c, 0x33,
  0x44, 0x45, 0x6b, 0x77, 0x55, 0x2f, 0x6f, 0x48, 0x7a, 0x50, 0x77, 0x55,
  0x54, 0x36, 0x62, 0x6e, 0x79, 0x64, 0x70, 0x56, 0x63, 0x79, 0x53, 0x7a,
  0x64, 0x30, 0x65, 0x0a, 0x48, 0x55, 0x53, 0x53, 0x5a, 0x52, 0x35, 0x77,
  0x59, 0x53, 0x71, 0x5a, 0x34, 0x57, 0x45, 0x31, 0x44, 0x74, 0x4e, 0x55,
  0x51, 0x45, 0x6a, 0x55, 0x57, 0x5a, 0x46, 0x6c, 0x6b, 0x53, 0x66, 0x62,
  0x76, 0x69, 0x43, 0x72, 0x79, 0x64, 0x4e, 0x4d, 0x6b, 0x4c, 0x34, 0x67,
  0x48, 0x46, 0x66, 0x49, 0x59, 0x33, 0x35, 0x70, 0x5a, 0x39, 0x68, 0x70,
  0x53, 0x74, 0x53, 0x4e, 0x45, 0x37, 0x64, 0x36, 0x0a, 0x4d, 0x77, 0x49,
  0x44, 0x41, 0x51, 0x41, 0x42, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x45,
  0x4e, 0x44, 0x20, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x20, 0x4b, 0x45,
  0x59, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a
};
unsigned int rsa_public_pem_len = 451;

esp_timer_handle_t auth_timer_handle;
#define SECURITY
/**
 * @brief GATT Write callback for file transfer characteristic.
 *
 * This callback is invoked when a BLE client writes data to the file
 * transfer characteristic. It receives chunks of data that can be
 * assembled to reconstruct the file on the server side.
 *
 * @param conn_handle    Connection handle identifying the BLE connection.
 * @param attr_handle    Attribute handle of the characteristic being written.
 * @param ctxt           Pointer to the BLE GATT access context. Contains
 *                       the operation type, data buffer, and other metadata.
 * @param arg            Optional user-defined argument passed during service
 *                       registration (can be NULL or a user context).
 *
 * @return               0 on success (BLE_ATT_ERR_SUCCESS), or an appropriate
 *                       BLE ATT error code on failure.
 */
int file_transfer_write_cb(uint16_t conn_handle, uint16_t attr_handle,
						   struct ble_gatt_access_ctxt *ctxt, void *arg)
{
	error_code_t status = BLE_ACK;
	/* TODO: HANDLE THE FW CHUNK SAVE TO FLASH */
	if(ctxt == NULL) {
		return BLE_ATT_ERR_INVALID_PDU;
	}
	#ifdef SECURITY
	if (is_authenticated == false)
	{
		/* ADD DISCONNECT HANDLER */
		ble_gap_terminate(conn_handle, BLE_ERR_REM_USER_CONN_TERM);
		return BLE_ATT_ERR_INVALID_HANDLE;
	}
	#endif
	switch (ctxt->op) {
	case BLE_GATT_ACCESS_OP_READ_CHR:
		return BLE_ATT_ERR_READ_NOT_PERMITTED;
		break;
	case BLE_GATT_ACCESS_OP_WRITE_CHR:
		status = ble_request_handler(ctxt->om->om_data);
		if ((status != ESP_OK)) {
			ESP_LOGE("ERROR", "%d", status);
			notify_client(conn_handle, req_resp_att_handle, status);
		}
		break;		
	default:
		break;
	}
	return 0;
}

/**
 * @brief GATT Read callback for the configuration file characteristic.
 *
 * This callback is invoked when a BLE client performs a read operation on
 * the configuration file characteristic. The function provides the configuration
 * data to the client in response to the read request.
 *
 * @param conn_handle    Connection handle identifying the BLE connection.
 * @param attr_handle    Attribute handle of the characteristic being read.
 * @param ctxt           Pointer to the BLE GATT access context. Contains
 *                       the operation type and the response buffer where
 *                       the server should append the data to send.
 * @param arg            Optional user-defined argument passed during service
 *                       registration (can be NULL or a user context pointer).
 *
 * @return               0 on success (BLE_ATT_ERR_SUCCESS), or an appropriate
 *                       BLE ATT error code on failure.
 */
int config_file_cb(uint16_t conn_handle, uint16_t attr_handle,
						struct ble_gatt_access_ctxt *ctxt, void *arg)
{
	error_code_t status = BLE_ACK;
	if(ctxt->om->om_len == 0) {
		notify_client(conn_handle, attr_handle, BLE_NACK);
		return 0;
	}
	#ifdef SECURITY
	if (is_authenticated == false && ota_mode_active == false)
	{
		/* ADD DISCONNECT HANDLER */
		ble_gap_terminate(conn_handle, BLE_ERR_REM_USER_CONN_TERM);
		return BLE_ATT_ERR_INVALID_HANDLE;
	}
	#endif
	const uint8_t *data = ctxt->om->om_data;
	#ifdef DEBUG_CMD
	ESP_LOGI("BLE", "Data Length: %d", ctxt->om->om_len);
	#endif
	/* CHANGE BASED ON READ WRITE */
	switch (ctxt->op)
	{
	case BLE_GATT_ACCESS_OP_READ_CHR:
		return BLE_ATT_ERR_READ_NOT_PERMITTED;
		break;
	case BLE_GATT_ACCESS_OP_WRITE_CHR:
		ESP_LOGI("BLE", "Sending Data to BLE Request Handler");
		status = ble_request_handler(data);
		switch (status) {
		case ESP_OK:
			break;
		case BLE_ACK:
			notify_client(conn_handle, attr_handle, status);
			break;	
		default:
			ESP_LOGE("ERROR", "%d", status);
			notify_client(conn_handle, attr_handle, status);
			break;
		}		
		led_set_ble_status(LED_STATE_BLINK_SLOW);  // BLUE LED: Show BLE is talking
		break;
	default:
		break;
	}
	return 0;
}

/**
 * @brief GATT Read callback for request-response characteristic.
 *
 * This callback is invoked when a BLE client performs a read operation on
 * the request-response characteristic. The server generates a response
 * dynamically based on the current state, a prior write request, or internal logic.
 *
 * @param conn_handle    Connection handle identifying the BLE connection.
 * @param attr_handle    Attribute handle of the characteristic being accessed.
 * @param ctxt           Pointer to the BLE GATT access context. Contains
 *                       the operation type and the response buffer to append
 *                       response data to.
 * @param arg            Optional user-defined argument passed during service
 *                       or characteristic registration (can be NULL or a user context).
 *
 * @return               0 on success (BLE_ATT_ERR_SUCCESS), or an appropriate
 *                       BLE ATT error code on failure.
 */
int request_response_cb(uint16_t conn_handle, uint16_t attr_handle,
						struct ble_gatt_access_ctxt *ctxt, void *arg)
{
	#ifdef SECURITY
	if (is_authenticated == false && ota_mode_active == false)
	{
		/* ADD DISCONNECT HANDLER */
		ble_gap_terminate(conn_handle, BLE_ERR_REM_USER_CONN_TERM);
		return BLE_ATT_ERR_INVALID_HANDLE;
	}
	#endif
	error_code_t status = BLE_ACK;
	uint32_t length = 0;
	const uint8_t *data = ctxt->om->om_data;
	#ifdef DEBUG_CMD
	ESP_LOGI("BLE", "Data Length: %d", ctxt->om->om_len);
	#endif
	/* CHANGE BASED ON READ WRITE */
	switch (ctxt->op)
	{
	case BLE_GATT_ACCESS_OP_READ_CHR:
#ifdef DEBUG_CMD
		ESP_LOGI("BLE", "Read Response");
#endif
		if (version_request_flag == true) {
			version_request_flag = false;
			data = get_version_info(&length);
			os_mbuf_append(ctxt->om, data, length);
		}
		else {
			notify_client(conn_handle, attr_handle, BLE_NACK);
		}
		break;
	case BLE_GATT_ACCESS_OP_WRITE_CHR:
		if(ctxt->om->om_len == 0) {
			notify_client(conn_handle, attr_handle, BLE_NACK);
			return 0;
		}
		status = ble_request_handler(data);
		switch (status) {
		case ESP_OK:
			break;
		case BLE_ACK:
			notify_client(conn_handle, attr_handle, status);
		default:
			break;
		}
		if ((status != ESP_OK)) {
			notify_client(conn_handle, attr_handle, status);
		}
		break;
	default:
		break;
	}
	return 0;
}

/**
 * @brief GATT Read callback for the file descriptor characteristic.
 *
 * This callback is invoked when a BLE client performs a read operation on
 * the file descriptor characteristic. It returns metadata about the file,
 * such as file size, CRC, file type, version, or any descriptor information
 * required before initiating a file transfer.
 *
 * @param conn_handle    Connection handle identifying the BLE connection.
 * @param attr_handle    Attribute handle of the characteristic being read.
 * @param ctxt           Pointer to the BLE GATT access context. Contains
 *                       the operation type and the response buffer where
 *                       the server should append the descriptor data.
 * @param arg            Optional user-defined argument passed during service
 *                       or characteristic registration (can be NULL or a context pointer).
 *
 * @return               0 on success (BLE_ATT_ERR_SUCCESS), or an appropriate
 *                       BLE ATT error code on failure.
 */
int file_descriptor_read_cb(uint16_t conn_handle, uint16_t attr_handle,
							struct ble_gatt_access_ctxt *ctxt, void *arg)
{
	if(ctxt->om->om_len == 0) {
		notify_client(conn_handle, attr_handle, BLE_NACK);
		return 0;
	}
	#ifdef SECURITY
	if (is_authenticated == false && ota_mode_active == false)
	{
		/* ADD DISCONNECT HANDLER */
		ble_gap_terminate(conn_handle, BLE_ERR_REM_USER_CONN_TERM);
		return BLE_ATT_ERR_INVALID_HANDLE;
	}
	#endif
	const char *desc = "File Transfer Charat";
#ifdef DEBUG_CMD
	ESP_LOGI("BLE", "Description Called");
#endif
	os_mbuf_append(ctxt->om, desc, strlen(desc));
	return 0;
}

void auth_timer_callback(void* arg) {
	ble_gap_terminate(get_connection_handle(), BLE_ERR_REM_USER_CONN_TERM);
    ESP_LOGI("AUTH", "EXITING AS NO SIGNATURE RECEIVED");
}

void auth_timer_start(uint32_t time_in_ms) {
    esp_timer_start_once(auth_timer_handle, time_in_ms * 1000);
}

void auth_timer_stop(void) {
    esp_timer_stop(auth_timer_handle);
}

esp_err_t auth_timer_init(void) {
    const esp_timer_create_args_t timer_args = {
        .callback = &auth_timer_callback,
        .arg = NULL,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "5_seconds_timer"
    };

    esp_err_t err = esp_timer_create(&timer_args, &auth_timer_handle);
    if (err != ESP_OK) {
        ESP_LOGE("Timer", "Failed to create timer: %s", esp_err_to_name(err));
    }
    return err;
}

int auth_cb(uint16_t conn_handle, uint16_t attr_handle,
							struct ble_gatt_access_ctxt *ctxt, void *arg)
{
	if(ctxt == NULL) {
		return BLE_ATT_ERR_INVALID_PDU;
	}
	
	switch (ctxt->op) {
	case BLE_GATT_ACCESS_OP_READ_CHR:
		ESP_LOGI("AUTH", "Read Challenge Start");
		mbedtls_entropy_context entropy = {0};
		mbedtls_ctr_drbg_context ctr_drbg = {0};
		const char *pers = "rsa_challenge";

		mbedtls_entropy_init(&entropy);
		mbedtls_ctr_drbg_init(&ctr_drbg);
		mbedtls_ctr_drbg_seed(&ctr_drbg, mbedtls_entropy_func, &entropy,
							(const unsigned char *)pers, strlen(pers));
		mbedtls_ctr_drbg_random(&ctr_drbg, challenge_buf, 16);
		challenge_len = 16;
    	os_mbuf_append(ctxt->om, challenge_buf, challenge_len);
		ESP_LOGI("AUTH", "Read Challenge End");
		// Start auth timer only if OTA mode is not active
		if (!ota_mode_active && auth_timer_init() == ESP_OK) {
			auth_timer_start(5000);
			ESP_LOGI("AUTH", "Authentication timer started (5 seconds)");
		} else if (ota_mode_active) {
			ESP_LOGI("AUTH", "Skipping auth timer - OTA mode active");
		}
		break;
	case BLE_GATT_ACCESS_OP_WRITE_CHR:
		ESP_LOGI("AUTH", "Write Challenge Start");
		
		if (challenge_len == 0) return BLE_ATT_ERR_INVALID_HANDLE;
		// Load public key
		mbedtls_pk_context pk = {0};
		mbedtls_pk_init(&pk);
		int rc = 0;
		rc = mbedtls_pk_parse_public_key(&pk, rsa_public_pem, strlen((const char *)rsa_public_pem) + 1);
		ESP_LOGI("AUTH", "Write Challenge %d", rc);
		// Hash challenge
		uint8_t hash[32];
		mbedtls_sha256(challenge_buf, challenge_len, hash, 0);
		ESP_LOGI("AUTH", "Write Challenge Hash Calculates");
		// Verify signature
		if (mbedtls_pk_verify(&pk, MBEDTLS_MD_SHA256, hash, 0,
							ctxt->om->om_data, ctxt->om->om_len) == 0) {
			is_authenticated = true;
			auth_timer_stop();
			ESP_LOGI("AUTH", "Device authenticated.\n");
			notify_client(conn_handle, attr_handle, BLE_ACK);
			challenge_len = 0;
			return 0;
		}
		else {
			notify_client(conn_handle, attr_handle, BLE_NACK);
			ESP_LOGE("AUTH", "Signature invalid.\n");
		}
		
		break;
	default:
		break;
	}
	return 0;
}

/**
 * @brief Enable OTA mode to bypass security checks
 */
void enable_ota_mode(void) {
    ota_mode_active = true;
    auth_timer_stop();  // Stop authentication timer during OTA
    ESP_LOGI("SECURITY", "OTA mode enabled - security checks bypassed, auth timer stopped");
}

/**
 * @brief Disable OTA mode to re-enable security checks
 */
void disable_ota_mode(void) {
    ota_mode_active = false;
    ESP_LOGI("SECURITY", "OTA mode disabled - security checks re-enabled");
}

/**
 * @brief Sends a BLE notification to the client with an error or status code.
 *
 * This function sends a notification to the connected BLE client on the
 * specified characteristic. It is typically used to inform the client about
 * asynchronous events, transfer status, errors, or completion statuses
 * during operations like file transfer or command execution.
 *
 * @param conn_handle    Connection handle identifying the BLE connection.
 * @param attr_handle    Attribute handle of the characteristic used for notification.
 * @param err_code       Error or status code to notify the client.
 *
 * @return               None
 */
void notify_client(uint16_t conn_handle, uint16_t attr_handle, uint8_t err_code)
{
	int32_t rt = err_code;
	struct os_mbuf *om = ble_hs_mbuf_from_flat(&rt, 1);
	ble_gattc_notify_custom(conn_handle, attr_handle, om);
}

/**
 * @brief Sends a BLE notification to the client with data.
 *
 * This function sends a notification to the connected BLE client on the
 * specified characteristic. It is typically used to inform the client about
 * data send from Nexus
 *
 * @param conn_handle    Connection handle identifying the BLE connection.
 * @param attr_handle    Attribute handle of the characteristic used for notification.
 * @param data       	 Data received
 * @param len			 length of data
 *
 * @return               None
 */
void notify_client_data(uint16_t conn_handle, uint16_t attr_handle, uint8_t* data, uint16_t len)
{
	struct os_mbuf *om = ble_hs_mbuf_from_flat(&data, len);
	ble_gattc_notify_custom(conn_handle, attr_handle, om);
}