/*
 * SPDX-FileCopyrightText: 2015-2021 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/*
 * Standard ESP-IDF OTA Manager Header
 * This follows the official ESP-IDF OTA patterns and APIs
 */

#ifndef OTA_MANAGER_H
#define OTA_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "esp_ota_ops.h"
#include "ble_ota_service.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Standard ESP-IDF OTA Configuration */
#define OTA_MAX_CHUNK_SIZE          (1024)

/* Standard ESP-IDF OTA Function Prototypes */

/**
 * @brief Initialize OTA manager (Standard ESP-IDF approach)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_init(void);

/**
 * @brief Start OTA process (calls esp_ota_begin)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_start(void);

/**
 * @brief Process firmware chunk (calls esp_ota_write)
 * @param data Chunk data
 * @param length Chunk length
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_process_chunk(const uint8_t *data, uint16_t length);

/**
 * @brief Complete OTA process (calls esp_ota_end + esp_ota_set_boot_partition)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_complete(void);

/**
 * @brief Handle OTA command (Standard ESP-IDF approach)
 * @param cmd OTA command to handle
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_handle_command(ota_command_t cmd);

/**
 * @brief Stop/abort OTA process (calls esp_ota_abort)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_stop(void);

/**
 * @brief Get OTA progress information
 * @param bytes_received Pointer to store bytes received
 * @param total_size Pointer to store total size
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_get_progress(uint32_t *bytes_received, uint32_t *total_size);

/* Compatibility functions for existing BLE OTA service */

/**
 * @brief Get current OTA status
 * @return Current OTA status
 */
ota_status_t ota_manager_get_status(void);

/**
 * @brief Get bytes received so far
 * @return Number of bytes received
 */
uint32_t ota_manager_get_bytes_received(void);

/**
 * @brief Get detailed status response
 * @param response Pointer to store status response
 */
void ota_manager_get_status_response(ota_status_response_t *response);

/**
 * @brief Set firmware information
 * @param info Firmware information structure
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_set_firmware_info(const ota_firmware_info_t *info);

/**
 * @brief Get firmware information
 * @param info Pointer to store firmware information
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_get_firmware_info(ota_firmware_info_t *info);

/**
 * @brief Reset OTA state (for recovery from stuck states)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t ota_manager_reset(void);

#ifdef __cplusplus
}
#endif

#endif /* OTA_MANAGER_H */
