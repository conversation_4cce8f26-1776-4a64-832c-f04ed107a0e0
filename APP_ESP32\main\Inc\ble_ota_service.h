/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_ota_service.h
* @version        : 1.0.0
* @brief          : Header for BLE OTA service
* @details        : BLE OTA service for firmware updates via NimBLE
********************************************************************************/

#ifndef BLE_OTA_SERVICE_H
#define BLE_OTA_SERVICE_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "nimble/ble.h"
#include "host/ble_hs.h"

/* Debug Configuration */
#define BLE_OTA_DEBUG_VERBOSE   0  // Set to 1 for detailed logs, 0 for minimal logs
#define BLE_OTA_DEBUG_PROGRESS  1  // Set to 1 for progress logs, 0 to disable

#if BLE_OTA_DEBUG_VERBOSE
    #define BLE_OTA_LOGV(tag, format, ...) ESP_LOGI(tag, format, ##__VA_ARGS__)
#else
    #define BLE_OTA_LOGV(tag, format, ...)
#endif

#if BLE_OTA_DEBUG_PROGRESS
    #define BLE_OTA_LOGP(tag, format, ...) ESP_LOGI(tag, format, ##__VA_ARGS__)
#else
    #define BLE_OTA_LOGP(tag, format, ...)
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* OTA Service and Characteristic UUIDs */
#define BLE_OTA_SERVICE_UUID        0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF0, 0xDE, \
                                    0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF0, 0xDE

#define BLE_OTA_DATA_CHAR_UUID      0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF1, 0xDE, \
                                    0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF0, 0xDE

#define BLE_OTA_CONTROL_CHAR_UUID   0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF2, 0xDE, \
                                    0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF0, 0xDE

#define BLE_OTA_STATUS_CHAR_UUID    0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF3, 0xDE, \
                                    0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF0, 0xDE

#define BLE_OTA_INFO_CHAR_UUID      0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF4, 0xDE, \
                                    0x34, 0x12, 0x78, 0x56, 0xBC, 0x9A, 0xF0, 0xDE

/* OTA Commands */
typedef enum {
    OTA_CMD_START = 0x01,
    OTA_CMD_STOP = 0x02,
    OTA_CMD_END = 0x03,
    OTA_CMD_ABORT = 0x04,
    OTA_CMD_RESUME = 0x05,
    OTA_CMD_VERIFY = 0x06
} ota_command_t;

/* OTA Status Codes */
typedef enum {
    OTA_STATUS_IDLE = 0x00,
    OTA_STATUS_READY = 0x01,
    OTA_STATUS_RECEIVING = 0x02,
    OTA_STATUS_VERIFYING = 0x03,
    OTA_STATUS_SUCCESS = 0x04,
    OTA_STATUS_ERROR = 0x05,
    OTA_STATUS_ACK = 0x06,
    OTA_STATUS_NACK = 0x07
} ota_status_t;

/* OTA Error Codes */
typedef enum {
    OTA_ERROR_NONE = 0x00,
    OTA_ERROR_INVALID_SIZE = 0x01,
    OTA_ERROR_WRITE_FAILED = 0x02,
    OTA_ERROR_VERIFY_FAILED = 0x03,
    OTA_ERROR_PARTITION_FAILED = 0x04,
    OTA_ERROR_SEQUENCE_ERROR = 0x05,
    OTA_ERROR_CRC_MISMATCH = 0x06
} ota_error_t;

/* OTA Firmware Info Structure */
typedef struct {
    uint32_t total_size;
    uint32_t chunk_size;
    uint32_t crc32;
    uint8_t version[16];
} __attribute__((packed)) ota_firmware_info_t;

/* OTA Chunk Header */
typedef struct {
    uint32_t sequence_number;
    uint16_t chunk_size;
    uint16_t reserved;
} __attribute__((packed)) ota_chunk_header_t;

/* OTA Status Response */
typedef struct {
    uint8_t status;          // Force to 1 byte instead of enum
    uint8_t error;           // Force to 1 byte instead of enum
    uint32_t sequence_number;
    uint32_t bytes_received;
} __attribute__((packed)) ota_status_response_t;

/* Function Prototypes */
esp_err_t ble_ota_service_init(void);
esp_err_t ble_ota_service_deinit(void);
esp_err_t ble_ota_send_status(ota_status_t status, ota_error_t error, uint32_t sequence);
esp_err_t ble_ota_get_connection_handle(uint16_t *conn_handle);

/* Callback function types */
typedef void (*ota_progress_callback_t)(uint32_t bytes_received, uint32_t total_size);
typedef void (*ota_complete_callback_t)(bool success, ota_error_t error);

/* Register callbacks */
esp_err_t ble_ota_register_progress_callback(ota_progress_callback_t callback);
esp_err_t ble_ota_register_complete_callback(ota_complete_callback_t callback);

#ifdef __cplusplus
}
#endif

#endif /* BLE_OTA_SERVICE_H */
