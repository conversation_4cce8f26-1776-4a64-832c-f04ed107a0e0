#
# Partition Table
#
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partition_table_unit_test_app.csv"
CONFIG_PARTITION_TABLE_FILENAME="partition_table_unit_test_app.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

#
# Heap
#
CONFIG_HEAP_POISONING_COMPREHENSIVE=y

#
# Watchdog
#
CONFIG_ESP_TASK_WDT=y
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=n
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1=n

#
# ESP32-specific
#
CONFIG_IDF_TARGET_ESP32=y
CONFIG_ESP32_DEFAULT_CPU_FREQ_240=y
CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ=240

CONFIG_ESP32_XTAL_FREQ_AUTO=y

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_BAUD_921600B=y
CONFIG_ESPTOOLPY_COMPRESSED=y
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHFREQ="80m"
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_ESPTOOLPY_FLASHSIZE="4MB"
CONFIG_ESPTOOLPY_BEFORE_RESET=y
CONFIG_ESPTOOLPY_BEFORE="default_reset"
CONFIG_ESPTOOLPY_AFTER_RESET=y
CONFIG_ESPTOOLPY_AFTER_NORESET=n
CONFIG_ESPTOOLPY_AFTER="hard_reset"
CONFIG_ESPTOOLPY_MONITOR_BAUD_CONSOLE=y
CONFIG_ESPTOOLPY_FLASHSIZE="4MB"
CONFIG_ESPTOOLPY_FLASHSIZE_DETECT=n

CONFIG_ESP_CONSOLE_UART_NUM=0

#
# SPI Flash driver
#
CONFIG_SPI_FLASH_VERIFY_WRITE=n
CONFIG_SPI_FLASH_ENABLE_COUNTERS=n
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=y
CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS=y
CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS=n
CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED=n

#
# SPIFFS Configuration
#
CONFIG_SPIFFS_MAX_PARTITIONS=3

#
# SPIFFS Cache Configuration
#
CONFIG_SPIFFS_CACHE=y
CONFIG_SPIFFS_CACHE_WR=y
CONFIG_SPIFFS_CACHE_STATS=n
CONFIG_SPIFFS_PAGE_CHECK=y
CONFIG_SPIFFS_GC_MAX_RUNS=10
CONFIG_SPIFFS_GC_STATS=n
CONFIG_SPIFFS_PAGE_SIZE=256
CONFIG_SPIFFS_OBJ_NAME_LEN=32
CONFIG_SPIFFS_USE_MAGIC=y
CONFIG_SPIFFS_USE_MAGIC_LENGTH=y
CONFIG_SPIFFS_META_LENGTH=4
CONFIG_SPIFFS_USE_MTIME=n

#
# FAT Filesystem support
#
CONFIG_FATFS_CODEPAGE_DYNAMIC=n
CONFIG_FATFS_CODEPAGE_437=y
CONFIG_FATFS_CODEPAGE_720=n
CONFIG_FATFS_CODEPAGE_737=n
CONFIG_FATFS_CODEPAGE_771=n
CONFIG_FATFS_CODEPAGE_775=n
CONFIG_FATFS_CODEPAGE_850=n
CONFIG_FATFS_CODEPAGE_852=n
CONFIG_FATFS_CODEPAGE_855=n
CONFIG_FATFS_CODEPAGE_857=n
CONFIG_FATFS_CODEPAGE_860=n
CONFIG_FATFS_CODEPAGE_861=n
CONFIG_FATFS_CODEPAGE_862=n
CONFIG_FATFS_CODEPAGE_863=n
CONFIG_FATFS_CODEPAGE_864=n
CONFIG_FATFS_CODEPAGE_865=n
CONFIG_FATFS_CODEPAGE_866=n
CONFIG_FATFS_CODEPAGE_869=n
CONFIG_FATFS_CODEPAGE_932=n
CONFIG_FATFS_CODEPAGE_936=n
CONFIG_FATFS_CODEPAGE_949=n
CONFIG_FATFS_CODEPAGE_950=n
CONFIG_FATFS_CODEPAGE=437
CONFIG_FATFS_LFN_NONE=y
CONFIG_FATFS_LFN_HEAP=n
CONFIG_FATFS_LFN_STACK=n
CONFIG_FATFS_FS_LOCK=0
CONFIG_FATFS_TIMEOUT_MS=10000
CONFIG_FATFS_PER_FILE_CACHE=y

CONFIG_UNITY_FREERTOS_PRIORITY=5
CONFIG_UNITY_FREERTOS_CPU=0
CONFIG_UNITY_FREERTOS_STACK_SIZE=12000
CONFIG_UNITY_WARN_LEAK_LEVEL_GENERAL=255
CONFIG_UNITY_CRITICAL_LEAK_LEVEL_GENERAL=1024
CONFIG_UNITY_CRITICAL_LEAK_LEVEL_LWIP=4095
CONFIG_UNITY_ENABLE_FLOAT=y
CONFIG_UNITY_ENABLE_DOUBLE=y
CONFIG_UNITY_ENABLE_COLOR=y
CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER=y
CONFIG_UNITY_ENABLE_FIXTURE=y
CONFIG_UNITY_ENABLE_BACKTRACE_ON_FAIL=y

# BOOTLOADER
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y
