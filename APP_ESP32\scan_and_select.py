#!/usr/bin/env python3
"""
BLE Device Scanner with Interactive Selection
This script scans for devices and lets you select by number
Returns the selected MAC address for use in batch files
"""

import asyncio
import sys
from bleak import BleakScanner

async def scan_and_select_device(timeout=10.0):
    """Scan for target BLE devices and allow interactive selection"""
    print("Scanning for target BLE devices...")
    print("Looking for devices starting with: MR, CT, COP, or containing ESP32")
    print(f"Scanning for {timeout} seconds...\n")
    
    devices = await BleakScanner.discover(timeout=timeout)
    target_devices = []
    
    print(f"Found {len(devices)} BLE devices during scan:")
    for device in devices:
        device_name = device.name or "Unknown"
        print(f"  Device: {device_name} ({device.address})")
        
        # Check if this looks like your target device (MR, CT, COP, or ESP32)
        if (device.name and
            (device.name.startswith("MR") or
             device.name.startswith("CT") or
             device.name.startswith("COP") or
             "ESP32" in device.name or
             "ESP32_OTA" in device.name or
             device.name == "ESP32_OTA")):
            target_devices.append(device)
    
    print(f"\nTarget devices found:")
    if not target_devices:
        print("  No target devices found!")
        print("\nTroubleshooting tips:")
        print("  - Make sure your device is powered on")
        print("  - Ensure BLE advertising is enabled")
        print("  - Move closer to the device")
        print("  - Check device name starts with: MR, CT, COP, or contains ESP32")
        return None
    
    # Display numbered list
    for i, device in enumerate(target_devices, 1):
        name = device.name or "Unknown"
        rssi_info = f" (RSSI: {device.rssi})" if hasattr(device, 'rssi') and device.rssi else ""
        print(f"  [{i}] {name} - {device.address}{rssi_info}")
    
    # Handle selection - Always show selection menu
    if len(target_devices) == 0:
        print("No target devices found.")
        return None

    print(f"\nFound {len(target_devices)} target device(s):")
    for i, device in enumerate(target_devices, 1):
        print(f"  [{i}] {device.name} - {device.address}")

    print(f"\nSelect device for firmware update:")
    print(f"Enter device number (1-{len(target_devices)}) or press Enter to cancel:")

    try:
        choice = input("Enter device number: ").strip()

        if not choice:  # User pressed Enter without input
            print("Selection cancelled.")
            return None

        try:
            device_num = int(choice)
            if 1 <= device_num <= len(target_devices):
                selected_device = target_devices[device_num - 1]
                print(f"\nSelected: {selected_device.name} - {selected_device.address}")
                return selected_device.address
            else:
                print(f"Invalid selection. Please enter a number between 1 and {len(target_devices)}.")
                return None
        except ValueError:
            print("Invalid input. Please enter a number.")
            return None

    except KeyboardInterrupt:
        print("\nSelection cancelled.")
        return None


async def main():
    try:
        selected_mac = await scan_and_select_device()
        if selected_mac:
            # Write selected MAC to file for batch script to read
            with open("selected_device.tmp", "w") as f:
                f.write(selected_mac)
            print(f"\nDevice selected: {selected_mac}")
        else:
            print("\nNo device selected.")
    except KeyboardInterrupt:
        print("\nScan interrupted by user")
    except Exception as e:
        print(f"Error during scan: {e}")

if __name__ == "__main__":
    asyncio.run(main())
