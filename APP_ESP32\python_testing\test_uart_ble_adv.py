#!/usr/bin/env python3
"""
UART Test Script for ESP32 BLE Name Updates

@file           : test_uart.py
@brief          : Python script to test dynamic BLE name updates via UART
@version        : 1.2.0
@date           : July 21, 2025
<AUTHOR> Vantage Elevation
@copyright      : Copyright (C) 2023-2025 by Vantage Elevation

@description    : This script simulates MR board communication with ESP32,
                  sending UART commands to dynamically update BLE advertising
                  names. It supports interactive testing and automated test
                  sequences.

@features       : - UART frame generation and parsing
                  - Dynamic BLE name updates via BLE_START_ADV/BLE_STOP_ADV
                  - Interactive testing mode
                  - Automated test sequences
                  - CRC32 verification with little-endian format
                  - ACK/NACK response handling
                  - 1 Mbps UART communication support

@usage          : python test_uart.py COM3 1000000 start "CT Car 1"
                  python test_uart.py COM3 1000000 interactive
"""

import serial
import struct
import time
import argparse
import sys
from typing import Optional

################################################################################
# CONSTANTS AND CONFIGURATION
################################################################################

# UART Frame Constants (matching ESP32 utility.h)
START_BYTE = 0xA5      # Frame start delimiter
BLE_START_ADV = 0x10   # Command to start BLE advertising with name
BLE_STOP_ADV = 0x11    # Command to stop BLE advertising
BLE_ACK = 0x01         # Acknowledgment response
BLE_NACK = 0x02        # Negative acknowledgment response

################################################################################
# UTILITY FUNCTIONS
################################################################################

def crc32(data: bytes) -> int:
    """
    Calculate CRC32 checksum for data integrity verification.

    Args:
        data: Byte array to calculate checksum for

    Returns:
        32-bit CRC checksum as unsigned integer
    """
    import zlib
    return zlib.crc32(data) & 0xFFFFFFFF

def create_uart_frame(command: int, payload: bytes = b'') -> bytes:
    """
    Create UART frame with proper format
    Frame: [START_BYTE][COMMAND][LENGTH(4)][PAYLOAD][CRC(4)]
    """
    # Build frame without CRC first
    frame = bytearray()
    frame.append(START_BYTE)
    frame.append(command)
    
    # Add payload length (4 bytes, little endian)
    payload_length = len(payload)
    frame.extend(struct.pack('<I', payload_length))
    
    # Add payload
    frame.extend(payload)
    
    # Calculate and add CRC (4 bytes, little endian)
    crc = crc32(bytes(frame))
    frame.extend(struct.pack('<I', crc))
    
    return bytes(frame)

def send_ble_start_adv(ser: serial.Serial, ble_name: str) -> bool:
    """Send BLE_START_ADV command with dynamic name"""
    print(f"📡 Sending BLE_START_ADV with name: '{ble_name}'")
    
    # Convert name to bytes
    name_bytes = ble_name.encode('utf-8')
    
    if len(name_bytes) > 31:
        print(f"❌ Error: BLE name too long ({len(name_bytes)} > 31 chars)")
        return False
    
    # Create frame
    frame = create_uart_frame(BLE_START_ADV, name_bytes)
    
    # Print frame details
    print(f"📦 Frame details:")
    print(f"   Name: '{ble_name}' ({len(name_bytes)} bytes)")
    print(f"   Frame: {' '.join(f'{b:02X}' for b in frame)}")
    print(f"   Length: {len(frame)} bytes")
    
    # Send frame
    try:
        ser.write(frame)
        ser.flush()
        time.sleep(0.05)  # Optimized delay for 1 Mbps
        print(f"✅ Frame sent successfully")
        
        # Wait for response
        return wait_for_response(ser)
        
    except Exception as e:
        print(f"❌ Error sending frame: {e}")
        return False

def send_ble_stop_adv(ser: serial.Serial) -> bool:
    """Send BLE_STOP_ADV command"""
    print(f"📡 Sending BLE_STOP_ADV")
    
    # Create frame (no payload)
    frame = create_uart_frame(BLE_STOP_ADV)
    
    print(f"📦 Frame: {' '.join(f'{b:02X}' for b in frame)}")
    
    try:
        ser.write(frame)
        ser.flush()
        time.sleep(0.05)  # Optimized delay for 1 Mbps
        print(f"✅ Frame sent successfully")
        
        # Wait for response
        return wait_for_response(ser)
        
    except Exception as e:
        print(f"❌ Error sending frame: {e}")
        return False

def wait_for_response(ser: serial.Serial, timeout: float = 5.0) -> bool:
    """Wait for ACK/NACK response from ESP32"""
    print(f"⏳ Waiting for response...")

    start_time = time.time()
    response_buffer = bytearray()

    while time.time() - start_time < timeout:
        if ser.in_waiting > 0:
            data = ser.read(ser.in_waiting)
            response_buffer.extend(data)

            # Print raw response for debugging
            if len(response_buffer) > 0:
                hex_response = ' '.join(f'{b:02X}' for b in response_buffer)
                print(f"📨 Raw response: {hex_response}")

            # Check if we have enough data for a response
            if len(response_buffer) >= 2:
                # Look for START_BYTE and ACK/NACK pattern
                for i in range(len(response_buffer) - 1):
                    if response_buffer[i] == START_BYTE:
                        response_type = response_buffer[i + 1]

                        print(f"📦 Response found at position {i}:")
                        print(f"   Start byte: 0x{response_buffer[i]:02X}")
                        print(f"   Response type: 0x{response_type:02X}")

                        if response_type == BLE_ACK:
                            print(f"✅ Received ACK - Command successful!")
                            return True
                        elif response_type == BLE_NACK:
                            print(f"❌ Received NACK - Command failed!")
                            return False
                        else:
                            print(f"📨 Unknown response type: 0x{response_type:02X}")
                            # Continue looking for valid response
                            continue

                # If buffer gets too large, clear it to prevent memory issues
                if len(response_buffer) > 100:
                    print(f"🧹 Clearing large response buffer ({len(response_buffer)} bytes)")
                    response_buffer.clear()

        time.sleep(0.1)

    print(f"⏰ Response timeout after {timeout}s")
    return False

def interactive_mode(ser: serial.Serial):
    """Interactive mode for testing different BLE names"""
    print("\n🎮 Interactive Mode - Enter BLE names to test")
    print("Commands:")
    print("  start <name>  - Send BLE_START_ADV with name")
    print("  stop          - Send BLE_STOP_ADV")
    print("  quit          - Exit")
    print()
    
    while True:
        try:
            cmd = input("MR-Board> ").strip()
            
            if cmd.lower() in ['quit', 'exit', 'q']:
                break
            elif cmd.lower() == 'stop':
                send_ble_stop_adv(ser)
            elif cmd.lower().startswith('start '):
                name = cmd[6:].strip()
                if name:
                    send_ble_start_adv(ser, name)
                else:
                    print("❌ Please provide a BLE name: start <name>")
            elif cmd.lower() == 'start':
                print("❌ Please provide a BLE name: start <name>")
            else:
                print("❌ Unknown command. Use: start <name>, stop, or quit")
            
            print()  # Empty line for readability
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def test_predefined_names(ser: serial.Serial):
    """Test with predefined BLE names"""
    test_names = [
        "CT Car 1",
        "CT Car 2", 
        "CT Car 3",
        "MR Board A",
        "MR Board B",
        "Test Device"
    ]
    
    print(f"\n🧪 Testing {len(test_names)} predefined BLE names...")
    
    for i, name in enumerate(test_names, 1):
        print(f"\n--- Test {i}/{len(test_names)} ---")
        success = send_ble_start_adv(ser, name)
        
        if success:
            print(f"✅ Test {i} passed")
        else:
            print(f"❌ Test {i} failed")
        
        # Wait between tests
        if i < len(test_names):
            print("⏳ Waiting 3 seconds before next test...")
            time.sleep(3)
    
    print(f"\n🏁 All tests completed!")

def main():
    parser = argparse.ArgumentParser(description="UART Test Script for ESP32 BLE Name Updates")
    parser.add_argument("port", help="Serial port (e.g., COM3, /dev/ttyUSB0)")
    parser.add_argument("baudrate", type=int, help="Baud rate (e.g., 1000000)")
    parser.add_argument("command", choices=['start', 'stop', 'interactive', 'test'],
                       help="Command to execute")
    parser.add_argument("name", nargs='?', help="BLE name for 'start' command")

    args = parser.parse_args()

    # Debug: Print parsed arguments
    print(f"🔍 Parsed arguments:")
    print(f"   Port: {args.port}")
    print(f"   Baudrate: {args.baudrate}")
    print(f"   Command: {args.command}")
    print(f"   Name: {args.name}")
    print()

    # Validate arguments
    if args.command == 'start' and not args.name:
        print("❌ Error: 'start' command requires a BLE name")
        print("Usage: python test_uart.py COM3 1000000 start \"CT Car 5\"")
        sys.exit(1)
    
    print(f"🔌 Connecting to {args.port} at {args.baudrate} baud...")
    
    try:
        # Open serial connection
        ser = serial.Serial(
            port=args.port,
            baudrate=args.baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
        
        print(f"✅ Connected successfully!")

        # Clear buffers and stabilize connection for 1 Mbps
        ser.reset_input_buffer()
        ser.reset_output_buffer()
        time.sleep(0.2)  # Shorter delay for high speed

        # Additional settings for 1 Mbps reliability
        ser.write_timeout = 1
        ser.inter_byte_timeout = 0.001
        
        # Execute command
        if args.command == 'start':
            send_ble_start_adv(ser, args.name)
        elif args.command == 'stop':
            send_ble_stop_adv(ser)
        elif args.command == 'interactive':
            interactive_mode(ser)
        elif args.command == 'test':
            test_predefined_names(ser)
        
        ser.close()
        print(f"🔌 Serial connection closed")
        
    except serial.SerialException as e:
        print(f"❌ Serial error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⏹️  Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
