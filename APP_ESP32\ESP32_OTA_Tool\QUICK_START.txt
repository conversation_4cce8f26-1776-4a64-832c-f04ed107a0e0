ESP32 OTA Tool - Quick Start Guide
===================================

This tool allows you to update ESP32 firmware wirelessly via Bluetooth.

EASY METHOD (Drag & Drop):
1. Drag your firmware .bin file onto "ota_update.bat"
2. Follow the prompts
3. Wait for update to complete

COMMAND LINE METHOD:
1. Open Command Prompt in this folder
2. Run: nimble_ota_client.exe your_firmware.bin

SCAN FOR DEVICES:
- Double-click "ota_update.bat" and type "scan"
- Or run: nimble_ota_client.exe --scan

REQUIREMENTS:
- Windows 10/11 with Bluetooth enabled
- ESP32 device in OTA mode
- Firmware .bin file

TROUBLESHOOTING:
- If no devices found, ensure ESP32 is powered and advertising
- If update fails, try with --verbose flag for details
- For same version error, update firmware version and rebuild

For detailed help, see README.md
