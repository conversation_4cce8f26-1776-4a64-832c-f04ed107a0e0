@echo off
echo Manual build process for NimBLE OTA Client
echo.

echo Step 1: Testing Python environment...
python_testing\bin\python --version
if errorlevel 1 (
    echo Python not found in virtual environment
    pause
    exit /b 1
)

echo.
echo Step 2: Testing simple script...
python_testing\bin\python test_simple.py
if errorlevel 1 (
    echo Failed to run test script
    pause
    exit /b 1
)

echo.
echo Step 3: Installing PyInstaller...
python_testing\bin\python -m pip install pyinstaller --upgrade
if errorlevel 1 (
    echo Failed to install PyInstaller
    pause
    exit /b 1
)

echo.
echo Step 4: Testing PyInstaller installation...
python_testing\bin\python -c "import PyInstaller; print('PyInstaller installed successfully')"
if errorlevel 1 (
    echo PyInstaller import failed
    pause
    exit /b 1
)

echo.
echo Step 5: Building simple test executable...
python_testing\bin\python -m PyInstaller --onefile --console test_simple.py
if errorlevel 1 (
    echo Failed to build test executable
    pause
    exit /b 1
)

echo.
echo Step 6: Checking if test executable was created...
if exist "dist\test_simple.exe" (
    echo Test executable created successfully!
    echo Running test executable...
    dist\test_simple.exe
) else (
    echo Test executable not found
    pause
    exit /b 1
)

echo.
echo Step 7: Building main OTA client executable...
python_testing\bin\python -m PyInstaller --onefile --console nimble_ota_client.py
if errorlevel 1 (
    echo Failed to build OTA client executable
    pause
    exit /b 1
)

echo.
echo Step 8: Checking final executable...
if exist "dist\nimble_ota_client.exe" (
    echo.
    echo ========================================
    echo SUCCESS! Executable created successfully!
    echo ========================================
    echo.
    echo Location: dist\nimble_ota_client.exe
    for %%A in ("dist\nimble_ota_client.exe") do echo Size: %%~zA bytes
    echo.
    echo Test the executable:
    echo dist\nimble_ota_client.exe --help
    echo.
) else (
    echo Final executable not found
    pause
    exit /b 1
)

pause
