#!/usr/bin/env python3
"""
ESP32 Firmware Updater via BLE
Sends firmware files to ESP32 for OTA update using improved chunk handling
"""

import asyncio
import time
import os
import struct
import hashlib
from bleak import BleakClient, BleakScanner
from bleak.exc import BleakError

# Configuration from your existing setup
BLE_DEVICE_ADDRESS = "a0:85:e3:f1:8c:c6"  # Your ESP32 MAC address
DEVICE_NAME = "CT Car 2"

# BLE Characteristics
WRITE_CHAR_UUID = "A2BD0013-AD84-44BE-94BB-B289C6D34F32"
READ_CHAR_UUID = "A2BD0012-AD84-44BE-94BB-B289C6D34F32"

# OTA Protocol Constants
OTA_START_REQUEST = 0x10
OTA_DATA_CHUNK = 0x11
OTA_END_REQUEST = 0x12
OTA_STATUS_REQUEST = 0x13

OTA_START_RESPONSE = 0x20
OTA_DATA_ACK = 0x21
OTA_END_RESPONSE = 0x22
OTA_STATUS_RESPONSE = 0x23
OTA_ERROR_RESPONSE = 0x2F

# Configuration
CHUNK_SIZE = 500
MAX_RETRIES = 3
TIMEOUT_SECONDS = 30

class FirmwareUpdater:
    def __init__(self, device_address=None):
        self.device_address = device_address or BLE_DEVICE_ADDRESS
        self.client = None
        self.chunk_id = 0
        self.total_chunks = 0
        self.firmware_size = 0
        
    async def find_device(self):
        """Find ESP32 device if address not specified"""
        if self.device_address:
            return self.device_address
            
        print("🔍 Scanning for ESP32 device...")
        devices = await BleakScanner.discover(timeout=10.0)
        
        for device in devices:
            if device.name and DEVICE_NAME.lower() in device.name.lower():
                print(f"✅ Found device: {device.name} ({device.address})")
                return device.address
        
        print("❌ ESP32 device not found!")
        return None
    
    async def connect(self):
        """Connect to ESP32 device"""
        device_address = await self.find_device()
        if not device_address:
            return False
            
        try:
            print(f"🔗 Connecting to {device_address}...")
            self.client = BleakClient(device_address, timeout=15.0)
            await self.client.connect()
            
            if self.client.is_connected:
                print("✅ Connected successfully!")
                return True
            else:
                print("❌ Connection failed")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from ESP32"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("🔌 Disconnected")
    
    def create_ota_packet(self, packet_type, data=b'', chunk_id=0):
        """Create OTA protocol packet"""
        packet = bytearray()
        packet.append(0xAA)  # Start byte
        packet.append(packet_type)
        packet.extend(struct.pack('<I', len(data)))  # Length (little endian)
        packet.extend(struct.pack('<I', chunk_id))   # Chunk ID (little endian)
        packet.extend(data)
        
        # Simple checksum (you can enhance this)
        checksum = sum(packet) & 0xFFFFFFFF
        packet.extend(struct.pack('<I', checksum))
        
        return bytes(packet)
    
    async def send_packet(self, packet, expect_response=True):
        """Send packet and optionally wait for response"""
        try:
            await self.client.write_gatt_char(WRITE_CHAR_UUID, packet, response=False)
            
            if expect_response:
                # Wait a bit for response (you can implement proper response handling)
                await asyncio.sleep(0.1)
                
            return True
        except Exception as e:
            print(f"❌ Send failed: {e}")
            return False
    
    async def send_ota_start(self, firmware_size):
        """Send OTA start command"""
        print(f"📤 Sending OTA start command (size: {firmware_size} bytes)")
        
        size_data = struct.pack('<I', firmware_size)
        packet = self.create_ota_packet(OTA_START_REQUEST, size_data)
        
        return await self.send_packet(packet)
    
    async def send_firmware_chunk(self, chunk_data, chunk_id):
        """Send firmware data chunk"""
        packet = self.create_ota_packet(OTA_DATA_CHUNK, chunk_data, chunk_id)
        return await self.send_packet(packet)
    
    async def send_ota_end(self):
        """Send OTA end command"""
        print("📤 Sending OTA end command")
        packet = self.create_ota_packet(OTA_END_REQUEST)
        return await self.send_packet(packet)
    
    async def update_firmware(self, firmware_path):
        """Main firmware update function"""
        if not os.path.exists(firmware_path):
            print(f"❌ Firmware file not found: {firmware_path}")
            return False
        
        # Get firmware info
        self.firmware_size = os.path.getsize(firmware_path)
        self.total_chunks = (self.firmware_size + CHUNK_SIZE - 1) // CHUNK_SIZE
        
        print(f"📁 Firmware file: {firmware_path}")
        print(f"📊 Size: {self.firmware_size} bytes")
        print(f"📦 Total chunks: {self.total_chunks}")
        
        # Calculate MD5 for verification
        with open(firmware_path, 'rb') as f:
            firmware_md5 = hashlib.md5(f.read()).hexdigest()
        print(f"🔐 MD5: {firmware_md5}")
        
        # Connect to device
        if not await self.connect():
            return False
        
        try:
            # Send OTA start
            if not await self.send_ota_start(self.firmware_size):
                print("❌ Failed to send OTA start")
                return False
            
            print("✅ OTA start sent successfully")
            await asyncio.sleep(1)  # Give ESP32 time to prepare
            
            # Send firmware chunks
            start_time = time.time()
            self.chunk_id = 0
            
            with open(firmware_path, 'rb') as f:
                while True:
                    chunk_data = f.read(CHUNK_SIZE)
                    if not chunk_data:
                        break
                    
                    self.chunk_id += 1
                    retry_count = 0
                    
                    # Retry mechanism for each chunk
                    while retry_count < MAX_RETRIES:
                        if await self.send_firmware_chunk(chunk_data, self.chunk_id):
                            progress = (self.chunk_id * 100) // self.total_chunks
                            print(f"📦 Chunk {self.chunk_id}/{self.total_chunks} ({len(chunk_data)} bytes) - {progress}%")
                            break
                        else:
                            retry_count += 1
                            print(f"⚠️  Chunk {self.chunk_id} failed, retry {retry_count}/{MAX_RETRIES}")
                            await asyncio.sleep(0.5)
                    
                    if retry_count >= MAX_RETRIES:
                        print(f"❌ Chunk {self.chunk_id} failed after {MAX_RETRIES} retries")
                        return False
                    
                    # Small delay between chunks
                    await asyncio.sleep(0.05)
            
            # Send OTA end
            if not await self.send_ota_end():
                print("❌ Failed to send OTA end")
                return False
            
            end_time = time.time()
            transfer_time = end_time - start_time
            throughput = self.firmware_size / transfer_time if transfer_time > 0 else 0
            
            print("✅ Firmware transfer completed!")
            print(f"⏱️  Transfer time: {transfer_time:.2f} seconds")
            print(f"🚀 Throughput: {throughput:.0f} bytes/second")
            print("🔄 ESP32 will reboot with new firmware")
            
            return True
            
        except Exception as e:
            print(f"❌ Update failed: {e}")
            return False
        
        finally:
            await self.disconnect()

async def main():
    """Main function"""
    print("🚀 ESP32 Firmware Updater via BLE")
    print("=" * 40)
    
    # Look for firmware files in current directory and build directory
    firmware_files = []
    
    # Check current directory
    for file in os.listdir('.'):
        if file.endswith('.bin'):
            firmware_files.append(file)
    
    # Check build directory
    build_dir = '../build'
    if os.path.exists(build_dir):
        for file in os.listdir(build_dir):
            if file.endswith('.bin'):
                firmware_files.append(os.path.join(build_dir, file))
    
    if not firmware_files:
        print("❌ No .bin files found")
        print("Available files:")
        for file in os.listdir('.'):
            print(f"  {file}")
        return
    
    # Show available firmware files
    print("📁 Available firmware files:")
    for i, file in enumerate(firmware_files):
        size = os.path.getsize(file)
        print(f"  {i+1}. {file} ({size} bytes)")
    
    # Let user choose (or use first one for automation)
    if len(firmware_files) == 1:
        firmware_path = firmware_files[0]
        print(f"🎯 Using: {firmware_path}")
    else:
        try:
            choice = int(input("Choose firmware file (number): ")) - 1
            if 0 <= choice < len(firmware_files):
                firmware_path = firmware_files[choice]
            else:
                print("❌ Invalid choice")
                return
        except (ValueError, KeyboardInterrupt):
            print("❌ Invalid input")
            return
    
    # Create updater and start update
    updater = FirmwareUpdater()
    success = await updater.update_firmware(firmware_path)
    
    if success:
        print("\n🎉 Firmware update completed successfully!")
        print("The ESP32 should reboot automatically with the new firmware.")
    else:
        print("\n❌ Firmware update failed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ Update interrupted by user")
