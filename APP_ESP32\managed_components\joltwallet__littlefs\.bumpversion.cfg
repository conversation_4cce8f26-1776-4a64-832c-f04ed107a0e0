[bumpversion]
current_version = 1.14.8
commit = True
tag = True

[bumpversion:file:README.md]
search = littlefs=={current_version}
replace = littlefs=={new_version}

[bumpversion:file:idf_component.yml]
search = "{current_version}"
replace = "{new_version}"

[bumpversion:file:library.json]
search = "{current_version}"
replace = "{new_version}"

[bumpversion:file(number):include/esp_littlefs.h]
search = ESP_LITTLEFS_VERSION_NUMBER "{current_version}"
replace = ESP_LITTLEFS_VERSION_NUMBER "{new_version}"

[bumpversion:file(major):include/esp_littlefs.h]
parse = (?P<major>\d+)
serialize = {major}
search = ESP_LITTLEFS_VERSION_MAJOR {current_version}
replace = ESP_LITTLEFS_VERSION_MAJOR {new_version}

[bumpversion:file(minor):include/esp_littlefs.h]
parse = (?P<minor>\d+)
serialize = {minor}
search = ESP_LITTLEFS_VERSION_MINOR {current_version}
replace = ESP_LITTLEFS_VERSION_MINOR {new_version}

[bumpversion:file(patch):include/esp_littlefs.h]
parse = (?P<patch>\d+)
serialize = {patch}
search = ESP_LITTLEFS_VERSION_PATCH {current_version}
replace = ESP_LITTLEFS_VERSION_PATCH {new_version}
